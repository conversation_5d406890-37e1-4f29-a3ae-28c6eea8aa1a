{"name": "chat-frontend", "version": "1.0.0", "description": "Frontend for real-time chat application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^4.1.0", "firebase": "^10.7.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}}